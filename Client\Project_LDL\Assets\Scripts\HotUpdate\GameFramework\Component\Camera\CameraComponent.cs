﻿using GameFramework.Event;
using System;
using DG.Tweening;
using UnityEngine;
using UnityEngine.Rendering.Universal;
using UnityGameFramework.Runtime;
namespace Game.Hotfix
{
    public enum BaseCamera : int
    {
        MainCamera,
        ArCamera,
        BattleCamera
    }

    [DisallowMultipleComponent]
    [AddComponentMenu("GameCustom/CameraComponent")]
    public partial class CameraComponent : GameFrameworkComponent
    {
        public static float ZoomPct;
        public static WorldMapLOD ZoomLevel;
        
        public bool IsFollowing => m_FollowTarget != null;
        public Transform FollowTrans => m_FollowTarget;
        
        /// <summary>
        /// 主相机
        /// </summary>
        private Camera m_MainCamera;
        private UniversalAdditionalCameraData m_MainCameraUniData;
        public Camera MainCamera
        {
            get => m_MainCamera;
            set => m_MainCamera = value;
        }
        private int m_sortingOrder = -1;

        private Camera m_CurUseCamera;
        public Camera CurUseCamera => m_CurUseCamera;

        /// <summary>
        /// 相机灵敏度
        /// </summary>
        private float CameraSensitivity = 0.1f;

        private OpModuleParam m_Param;

        private Transform m_FollowTarget;//摄像机追踪的物体
        
        protected override void Awake()
        {
            base.Awake();
            Debug.Log("CameraComponent  Awake");
            GameEntry.Event.Subscribe(OnSceneAwakeEventArgs.EventId, OnLoadSceneSuccess);
            GameEntry.Event.Subscribe(BeforeChangeSceneEventArgs.EventId, BeforeChangeSceneEvent);

            m_MainCamera = GameObject.Find("Main Camera").GetComponent<Camera>();
            m_MainCameraUniData = m_MainCamera.GetComponent<UniversalAdditionalCameraData>();
            m_CurUseCamera = m_MainCamera;
            OnUICameraAwark(m_MainCamera);
            //CinemachineCore.GetInputAxis = GetAxisCustom;
        }

        private void BeforeChangeSceneEvent(object sender, GameEventArgs e)
        {
            if (e is BeforeChangeSceneEventArgs a)
            {
                if (a.CurSceneId == (int)SceneDefine.MainScene)
                {
                    CacheCityCameraPos();
                }
            }
        }

        private void OnLoadSceneSuccess(object sender, GameEventArgs e)
        {
            OnSceneAwakeEventArgs a = (OnSceneAwakeEventArgs)e;
            if (a.SceneId == (int)SceneDefine.MainScene)
            {
                OnCityCameraAdd();
                m_CurUseCamera = m_CityCamera;
                OpModuleParam param = new OpModuleParam(); 
                param.camera = m_CityCamera;
                param.mapLimit = new Rect(29, 29, 75, 75);
                param.ZOOM_MAX = 25;
                param.ZOOM_MIN = 5;
                param.ZOOM_SPEED = 5;
                param.MAP_MAX_SIZE = 140;

                param.ZOOM_Value_P = 7;
                param.ZOOM_MAX_P = 310;
                param.ZOOM_MIN_P = 120;
                param.ZOOM_SPEED_P = 52;
                SetCameraParam(param);
            }

            if (a.SceneId == (int)SceneDefine.Battle5v5Scene)
            {
                OnBattleCameraAdd();
                m_CurUseCamera = m_BattleCamera;
                OpModuleParam param = new OpModuleParam();
                param.camera = m_BattleCamera;
                SetCameraParam(param);
            }

            if (a.SceneId == (int)SceneDefine.WorldMapScene)
            {
                OnWorldMapCameraAdd();
                m_CurUseCamera = m_WorldMapCamera;
                
                OpModuleParam param = new OpModuleParam();
                param.camera = m_WorldMapCamera;
                param.mapLimit = new Rect(0, 0, 1000, 1000);
                param.ZOOM_MAX = 25;
                param.ZOOM_MIN = 5;
                param.ZOOM_SPEED = 5;
                param.MAP_MAX_SIZE = 140;

                param.ZOOM_Value_P = 22;
                param.ZOOM_MAX_P = 1450;//透视
                param.ZOOM_MIN_P = 33;//透视
                param.ZOOM_SPEED_P = 100;//透视 （ZOOM_MAX_P-ZOOM_MIN_P)/5

                param.ZOOM_LEVEL_DEFINE = new[] { 87 / 1450f, 200 / 1450f,276 / 1450f, 326 / 1450f, 400 / 1450f, 1450 / 1450f };
                param.ZOOM_SPEED_DEFINE = new[] { 150f, 300f, 340f, 400f, 450f };
                SetCameraParam(param);
            }
        }

        public void RegistCamera(Camera camera,int sortingOrder)
        {
            camera.GetComponent<UniversalAdditionalCameraData>().renderType = CameraRenderType.Overlay;
            MainCamera.AddToCameraStack(camera, sortingOrder);
        }

        public void FollowTarget(Transform target)
        {
            m_FollowTarget = target;
        }
        
        public void LookAtPosition(Vector3 targetPosition, Action action = null, float duration = 0.5f)
        {
            if (m_CurUseCamera != null)
            {
                var curPosition = GetCurrentLookAtPosition();
                var moveOffset = targetPosition - curPosition;
                if(duration == 0)
                {
                    m_CurUseCamera.transform.position = m_CurUseCamera.transform.position + moveOffset;
                    action?.Invoke();
                    GameEntry.Event.Fire(this, OnWorldMapCameraMoveArgs.Create(false));
                }
                else
                {
                    m_CurUseCamera.transform.DOMove(m_CurUseCamera.transform.position + moveOffset, duration).OnComplete(
                        () =>
                        {
                            action?.Invoke();
                            GameEntry.Event.Fire(this, OnWorldMapCameraMoveArgs.Create(false));
                        }).OnUpdate(() =>
                    {
                        GameEntry.Event.Fire(this, OnWorldMapCameraMoveArgs.Create(false));
                    });
                }
            }
            else
            {
                Debug.LogError("m_MainCamera is Null");
            }
        }

        public void LookAtPosition(Vector3 targetPosition, float distance, Action action = null, float duration = 0.5f)
        {
            if (m_CurUseCamera != null)
            {
                var curDistance = Vector3.Distance(MapGridUtils.GetCameraLookAtWorldPosition(m_CurUseCamera),
                    m_CurUseCamera.transform.position);
                var offset = m_CurUseCamera.transform.forward * (curDistance - distance);
                LookAtPosition(targetPosition + offset, () =>
                {
                    var newDis = Vector3.Distance(MapGridUtils.GetCameraLookAtWorldPosition(m_CurUseCamera),
                        m_CurUseCamera.transform.position);
                    CalculateZoom(newDis);
                    GameEntry.Event.Fire(this, OnWorldMapCameraZoomArgs.Create());
                    action?.Invoke();
                }, duration);
            }
        }

        public Vector3 GetCurrentLookAtPosition()
        {
            if (m_CurUseCamera != null)
            {
                return MapGridUtils.GetCameraLookAtWorldPosition(m_CurUseCamera);
            }
            else
            {
                Debug.LogError("m_MainCamera is Null");
                return Vector3.zero;
            }
        }

        private void SetCameraParam(OpModuleParam param)
        {
            m_Param = param;
        }

        public OpModuleParam GetCameraParam()
        {
            return m_Param;
        }
        
        public void CalculateZoom(float distance)
        {
            if (distance < m_Param.ZOOM_MIN_P)
            {
                ZoomPct = 0;
                ZoomLevel = WorldMapLOD.Level1;
            }
            else if(distance > m_Param.ZOOM_MAX_P)
            {
                ZoomPct = 1;
                ZoomLevel = WorldMapLOD.Level6;
            }
            else
            {
                ZoomPct = (distance - m_Param.ZOOM_MIN_P) / (m_Param.ZOOM_MAX_P - m_Param.ZOOM_MIN_P);
                ZoomLevel = m_Param.GetLODLevel(ZoomPct);
            }
        }
        
        
        private void OnDestroy()
        {
            //TODO HXQ
            //  GameEntry.Event.Unsubscribe(ChangeSceneHighPriorityEventArgs.EventId, OnLoadSceneSuccess);
            // GameEntry.Event.Unsubscribe(BeforeChangeSceneEventArgs.EventId, BeforeChangeSceneEvent);
        }

        private void LateUpdate()
        {
            if (m_FollowTarget)
            {
                LookAtPosition(m_FollowTarget.position, null, 0);
            }
        }
    }
}