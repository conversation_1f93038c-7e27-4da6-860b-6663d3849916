using System.Collections.Generic;
using System.Linq;
using DG.Tweening;
using Game.Hotfix.Config;
using Gift;
using UnityEngine;
using UnityEngine.Events;

//触发礼包、触发礼包
namespace Game.Hotfix
{
    public class LimitGiftData
    {
        public int DisplayId = 0;

        private Dictionary<int, string> iconPathDic;
        private List<GiftTrigger> giftTriggerList;

        private int timeRecord = 0;
        private long firstGiftTime = 0;
        private long displayTime = 0;
        private bool isDisplay = false;

        private List<GiftTrigger> displayList;

        public void Init()
        {
            isDisplay = false;
            giftTriggerList = new List<GiftTrigger>();
            displayList = new List<GiftTrigger>();
            iconPathDic = new Dictionary<int, string>();
            //触发礼包新增/修改推送
            RegisterProtoEvent(Protocol.MessageID.PushGiftTriggerChange, OnPushGiftTriggerChange);

            //触发礼包删除
            RegisterProtoEvent(Protocol.MessageID.PushGiftTriggerDel, OnPushGiftTriggerDel);
        }

        //注册协议推送逻辑
        private void RegisterProtoEvent(Protocol.MessageID id, UnityAction<object> callback = null)
        {
            var ProtoId = (int)id;
            NetEventDispatch.Instance.RegisterEvent(ProtoId, message => { callback?.Invoke(message); });
        }

        //判断触发礼包功能入口解锁初始化逻辑
        public void CheckLimitGiftEntry(GameObject entryObj)
        {
            C2SGiftTriggerListReq((resp) =>
            {
                var isUnlock = HasLimitGift();
                entryObj.gameObject.SetActive(isUnlock);
                if (isUnlock)
                {
                    SetLimitGiftUnlockView(entryObj);
                }
            });
        }

        public void SetLimitGiftUnlockView(GameObject entryObj)
        {
            var msg = giftTriggerList[0];
            var root = entryObj.transform;
            if (ToolScriptExtend.GetConfigById<gift_pack>(msg.Id, out var data))
            {
                root.Find("m_imgLimitIcon").GetComponent<UIImage>().SetImage(data.gift_entrance_icon);
                var time = giftTriggerList[0].ExpiredTime - TimeComponent.Now;
                root.Find("m_txtLimitTimer").GetComponent<UIText>().text = ToolScriptExtend.FormatTime((int)time);
            }
        }

        //循环播放限时礼包icon
        public void DisplayGiftIcon(GameObject root, UIImage icon, UIText txt)
        {
            if (!root.activeInHierarchy) return;
            if (!isDisplay) return;
            var sumCount = displayList.Count;
            if (sumCount == 0) return;
            if (sumCount != iconPathDic.Count) return;
            var msg = displayList[DisplayId];
            timeRecord++;
            if (timeRecord == 3)
            {
                if (iconPathDic.TryGetValue(msg.Id, out var data))
                {
                    if (sumCount > 1)
                    {
                        icon.DOFade(0, 0.3f).SetDelay(2.5f).OnComplete(() => { icon.DOFade(1, 0.5f).SetDelay(0.2f); });

                        icon.SetImage(data);
                    }

                    var temp = DisplayId + 1;
                    if (temp == sumCount)
                    {
                        temp = 0;
                    }

                    DisplayId = temp;
                    displayTime = displayList[DisplayId].ExpiredTime - TimeComponent.Now;
                }

                timeRecord = 0;
            }

            displayTime--;
            txt.text = ToolScriptExtend.FormatTime((int)displayTime);
        }

        #region 协议

        //协议返回数据打印
        private void ProtoLog(bool isRequest, string protoName, object data)
        {
            bool isShow = true;
            if (isShow)
            {
                ColorLog.ProtoLog(isRequest, protoName, data);
            }
        }

        //触发礼包列表请求
        public void C2SGiftTriggerListReq(UnityAction<GiftTriggerListResp> callback = null)
        {
            isDisplay = false;
            var req = new GiftTriggerListReq();
            ProtoLog(true, "触发礼包列表", req);
            GameEntry.LDLNet.Send(Protocol.MessageID.GiftTriggerList, req, (message) =>
            {
                var resp = (GiftTriggerListResp)message;
                ProtoLog(false, "触发礼包列表", resp);
                giftTriggerList.Clear();
                giftTriggerList.AddRange(resp.List);
                UpdateIconList();
                UpdateDisplayMsg();
                callback?.Invoke(resp);
                ToolScriptExtend.CheckAndRefreshForm<string>(EnumUIForm.UILimitGiftForm, "Refresh|0");
            });
        }

        //触发礼包新增/修改推送
        private void OnPushGiftTriggerChange(object message)
        {
            isDisplay = false;
            var resp = (PushGiftTriggerChange)message;
            ProtoLog(false, "触发礼包新增/修改推送", resp);
            if (giftTriggerList == null) return;
            if (resp?.List == null) return;
            if (resp.List.Count == 0) return;
            
            var popList = new List<GiftTrigger>();
            foreach (var data in resp.List)
            {
                var index = giftTriggerList.FindIndex(x => x.Id == data.Id);
                if (index == -1)
                {
                    //礼包新增
                    popList.Add(data);
                    giftTriggerList.Add(data);
                }
                else
                {
                    //礼包修改
                    giftTriggerList[index] = data;
                }
            }

            UpdateIconList();
            UpdateDisplayMsg();
            GameEntry.Event.Fire(LimitGiftChangeEventArgs.EventId, LimitGiftChangeEventArgs.Create());
            var form = GameEntry.UI.GetUIForm(EnumUIForm.UILimitGiftForm);
            if (form == null)
            {
                if (popList.Count > 0)
                {
                    GameEntry.UI.OpenUIForm(EnumUIForm.UILimitGiftForm,
                        new LimitGiftParams() { isNewAdd = true, giftList = popList });
                }
            }
            else
            {
                ToolScriptExtend.CheckAndRefreshForm<string>(EnumUIForm.UILimitGiftForm, $"Refresh|{resp.List[0].Id}");
            }
        }

        //触发礼包删除推送
        private void OnPushGiftTriggerDel(object message)
        {
            isDisplay = false;
            var resp = (PushGiftTriggerDel)message;
            ProtoLog(false, "触发礼包删除推送", resp);
            if (resp.List.Count == 0) return;
            giftTriggerList.RemoveAll(x => resp.List.Contains(x.Id));
            UpdateIconList();
            UpdateDisplayMsg();
            ToolScriptExtend.CheckAndRefreshForm<string>(EnumUIForm.UILimitGiftForm, "Refresh|0");
            GameEntry.Event.Fire(LimitGiftChangeEventArgs.EventId, LimitGiftChangeEventArgs.Create());
        }

        #endregion

        public List<GiftTrigger> GetGiftList(bool needSort = true)
        {
            var list = new List<GiftTrigger>();
            foreach (var gift in giftTriggerList)
            {
                if (ToolScriptExtend.GetConfigById<gift_pack>(gift.Id, out var data))
                {
                    if (gift.BuyTimes < data.buy_limit_times)
                    {
                        if (gift.ExpiredTime == -1)
                        {
                            if (CheckGiftUnlock(gift.Id))
                            {
                                list.Add(gift);
                            }
                        }
                        else
                        {
                            if (TimeComponent.Now < gift.ExpiredTime)
                            {
                                if (CheckGiftUnlock(gift.Id))
                                {
                                    list.Add(gift);
                                }
                            }
                        }
                    }
                }
            }

            if (needSort)
            {
                list.Sort((a, b) =>
                {
                    if (a.ExpiredTime != b.ExpiredTime) return a.ExpiredTime.CompareTo(b.ExpiredTime);
                    ToolScriptExtend.GetConfigById<gift_pack>(a.Id, out var dataA);
                    ToolScriptExtend.GetConfigById<gift_pack>(b.Id, out var dataB);
                    if (dataA.priority != dataB.priority) return dataA.priority.CompareTo(dataB.priority);
                    return a.Id.CompareTo(b.Id);
                });
            }

            return list;
        }

        //礼包是否存在
        public bool IsExistGift(int giftId)
        {
            var list = GetGiftList(false);
            return list.Any(x => x.Id == giftId);
        }

        //是否有触发礼包
        public bool HasLimitGift()
        {
            if (giftTriggerList.Count == 0) return false;
            foreach (var gift in giftTriggerList)
            {
                if (CheckGiftUnlock(gift.Id))
                {
                    return true;
                }
            }

            return false;
        }

        //更新图标列表
        private void UpdateIconList()
        {
            iconPathDic.Clear();
            displayList.Clear();
            var list = GetGiftList();
            displayList.AddRange(list);
            foreach (var gift in list)
            {
                if (ToolScriptExtend.GetConfigById<gift_pack>(gift.Id, out var data))
                {
                    iconPathDic[gift.Id] = data.gift_entrance_icon;
                }
            }
        }

        private void UpdateDisplayMsg()
        {
            DisplayId = 0;
            if (giftTriggerList.Count > 0)
            {
                displayTime = giftTriggerList[0].ExpiredTime - TimeComponent.Now;
            }
            else
            {
                displayTime = 0;
            }

            isDisplay = true;
        }

        //判断礼包是否满足显示条件
        private bool CheckGiftUnlock(int giftId)
        {
            if (ToolScriptExtend.GetConfigById<gift_pack>(giftId, out var data))
            {
                if (data.gift_pack_type == paymenttype.paymenttype_theme)
                {
                    if (ToolScriptExtend.CheckDemandUnlockList(data.display_demand))
                    {
                        return true;
                    }
                }
                else
                {
                    return true;
                }
            }

            return false;
        }
    }
}