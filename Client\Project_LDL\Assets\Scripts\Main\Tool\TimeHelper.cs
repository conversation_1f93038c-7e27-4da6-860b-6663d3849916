using System;

public static class TimeHelper
{
    private static readonly DateTime UnixEpoch = TimeZone.CurrentTimeZone.ToLocalTime(new DateTime(1970, 1, 1));

    public static DateTime GetDateTime(long ts)
    {
        var dtStart = TimeZone.CurrentTimeZone.ToLocalTime(new DateTime(1970, 1, 1));
        var ticks = ts * 10000000;
        var toNow = new TimeSpan(ticks);
        return dtStart.Add(toNow);
    }

    /// <summary>
    /// 显示服务器传回来的时间；(lua有用到)
    /// </summary>
    /// <param name="ts">服务器传回的 create_time 或者其它ts</param>
    /// <param name="format"></param>
    /// <returns></returns>
    public static string ToDateTimeText(long ts, string format = "yyyy-MM-dd HH:mm:ss")
    {
        var targetDt = GetDateTime(ts);
        return targetDt.ToString(format);
    }

    public static string ToDateUTCTimeText(long ts, string format = "yyyy-MM-dd HH:mm:ss")
    {
        var targetDt = GetDateTime(ts);
        var utcTime = targetDt.ToUniversalTime();
        return utcTime.ToString(format);
    }

    /// <summary>
    /// 计划当前时间的UTC时间戳
    /// </summary>
    /// <param name="ts">当前时间戳</param>
    /// <returns></returns>
    public static long ToUtcTimeTs(long ts)
    {
        var targetDt = GetDateTime(ts);
        var utcTime = targetDt.ToUniversalTime();
        return ts - (targetDt.Ticks - utcTime.Ticks) / 10000000;
    }

    /// <summary>
    /// 获取当前时区；
    /// </summary>
    /// <returns></returns>
    public static string UtcLocal()
    {
        var utcTime = DateTime.Now.ToUniversalTime();
        var ts = new TimeSpan(DateTime.Now.Ticks - utcTime.Ticks);
        return ((int)ts.TotalHours).ToString();
    }

    /// <summary>
    /// (lua有用到)
    /// </summary>
    /// <param name="format"></param>
    /// <returns></returns>
    public static string DateTimeToString(string format)
    {
        return DateTime.Now.ToString(format);
    }

    public static string FormatGameTime(int time, bool showHour = false)
    {
        var min = time / 60;
        var second = time % 60;
        if (!showHour)
        {
            if (min >= 60)
            {
                min = 59;
                second = 59;
            }

            return $"{min:00}:{second:00}";
        }

        var hour = min / 60;
        return $"{hour:00}:{min:00}:{second:00}";
    }

    /// <summary>
    /// 输出: 1d 00:00:05
    /// </summary>
    /// <param name="second"></param>
    /// <returns></returns>
    public static string FormatGameTimeWithDays(int second)
    {
        TimeSpan timeSpan = TimeSpan.FromSeconds(second);
        if (timeSpan.TotalDays >= 1)
        {
            return string.Format("{0}d {1:D2}:{2:D2}:{3:D2}",
                timeSpan.Days,
                timeSpan.Hours,
                timeSpan.Minutes,
                timeSpan.Seconds);
        }
        else
        {
            return string.Format("{0:D2}:{1:D2}:{2:D2}",
                timeSpan.Hours,
                timeSpan.Minutes,
                timeSpan.Seconds);
        }
    }

    /// <summary>
    /// 输出: 1d 00:00
    /// </summary>
    /// <param name="second"></param>
    /// <returns></returns>
    public static string FormatGameTime2(int second)
    {
        TimeSpan timeSpan = TimeSpan.FromSeconds(second);
        if (timeSpan.TotalDays >= 1)
        {
            return string.Format("{0}d {1:D2}:{2:D2}",
                timeSpan.Days,
                timeSpan.Hours,
                timeSpan.Minutes);
        }
        else
        {
            return string.Format("{0:D2}:{1:D2}",
                timeSpan.Hours,
                timeSpan.Minutes);
        }
    }

    /// <summary>
    /// 将时间戳格式化为 "M-d H:mm" 格式（如：1-17 10:10）
    /// </summary>
    /// <param name="ts">时间戳</param>
    /// <returns>格式化的时间字符串</returns>
    public static string FormatToMonthDayTime(ulong seconds)
    {
        return FormatMillisecondsToDateTime(seconds, "M-d H:mm");
    }

    /// <summary>
    /// 将秒级时间戳转换为日期时间格式字符串
    /// </summary>
    /// <param name="milliseconds">秒级时间戳</param>
    /// <param name="format">日期时间格式，默认为 "yyyy-MM-dd HH:mm:ss"</param>
    /// <returns>格式化的日期时间字符串</returns>
    public static string FormatSecondsToDateTime(ulong seconds, string format = "yyyy-MM-dd HH:mm:ss")
    {
        // 获取 DateTimeOffset 对象，它包含时区偏移（默认是 UTC）
        DateTimeOffset dateTimeOffset = DateTimeOffset.FromUnixTimeSeconds((long)seconds);
        // 将其转换为本地时间并获取 DateTime
        DateTime localTime = dateTimeOffset.ToLocalTime().DateTime;
        // 将 DateTime 对象格式化为所需的字符串格式
        return localTime.ToString(format);
    }

    /// <summary>
    /// 将毫秒级时间戳转换为日期时间格式字符串
    /// </summary>
    /// <param name="milliseconds">毫秒级时间戳</param>
    /// <param name="format">日期时间格式，默认为 "yyyy-MM-dd HH:mm:ss"</param>
    /// <returns>格式化的日期时间字符串</returns>
    public static string FormatMillisecondsToDateTime(ulong milliseconds, string format = "yyyy-MM-dd HH:mm:ss")
    {
        // 获取 DateTimeOffset 对象，它包含时区偏移（默认是 UTC）
        DateTimeOffset dateTimeOffset = DateTimeOffset.FromUnixTimeMilliseconds((long)milliseconds);
        // 将其转换为本地时间并获取 DateTime
        DateTime localTime = dateTimeOffset.ToLocalTime().DateTime;
        // 将 DateTime 对象格式化为所需的字符串格式
        return localTime.ToString(format);
    }
}