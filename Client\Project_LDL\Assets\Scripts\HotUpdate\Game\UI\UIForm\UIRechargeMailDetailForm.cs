using System.Collections.Generic;
using System.Linq;
using Game.Hotfix.Config;
using Mail;
using UnityEngine;
using UnityEngine.Pool;
using UnityEngine.UI;

namespace Game.Hotfix
{
    public partial class UIRechargeMailDetailForm : UGuiFormEx
    {
        private MailData MailManager => GameEntry.LogicData.MailData;

        private List<MailBrief> MailBriefs;
        private mailbox_type curMailType;
        private float mailItemHeight;
        
        private int initCount = 5; //对象池初始化的数量
        private ObjectPool<GameObject> ObjPool;

        private List<float> posHeight;

        private List<int> LogicIdList;
        private List<int> lastLogicIdList;

        private Transform contentTrans;
        private float itemSpacing = 0;
        
        //初始化对象池
        private void InitPoolLogic()
        {
            LogicIdList = new List<int>();
            lastLogicIdList = new List<int>();
            ObjPool = new ObjectPool<GameObject>(() =>
                {
                    //将会在创建新对象的时侯调用
                    var obj = Instantiate(m_goRechargeMail, m_goPool.transform);
                    obj.SetActive(false);
                    obj.transform.localPosition = Vector3.zero;
                    return obj;
                },
                (go) =>
                {
                    // 会在通过池子获取对象的时侯调用，
                    go.SetActive(true);
                },
                (go) =>
                {
                    // 在对象放回池子里的时侯调用，这里我们取消激活需要放回池中的对象
                    go.SetActive(false);
                    var trans = go.transform;
                    trans.SetParent(m_goPool.transform);
                    trans.localPosition = Vector3.zero;
                },
                (go) =>
                {
                    Destroy(go);
                },true,initCount);
        }
        
        protected override void OnInit(object userData)
        {
            base.OnInit(userData);
            InitPoolLogic();
            MailBriefs = new List<MailBrief>();
            m_goPrefab.SetActive(false);
            curMailType = mailbox_type.mailbox_type_recharge;
            mailItemHeight = m_goRechargeMail.GetComponent<RectTransform>().rect.height;
            posHeight = new List<float>();
            contentTrans = m_scrollviewList.content.transform;
            InitBind();
        }

        protected override void OnOpen(object userData)
        {
            base.OnOpen(userData);
            m_scrollviewList.normalizedPosition = new Vector2(0, 1);
            posHeight.Clear();
            MailBriefs.Clear();
            LogicIdList.Clear();
            lastLogicIdList.Clear();
            UpdateIdList(0);
            InitPageView();
        }

        private void OnScrollFunc()
        {
            var curIndex = GetCurIndexPos(m_scrollviewList.content.anchoredPosition.y);
            UpdateIdList(curIndex);
            ShowLogicItemList();
        }

        private void UpdateIdList(int startIndex)
        {
            var endIndex = startIndex + initCount;
            LogicIdList.Clear();
            for (var i = startIndex; i < endIndex; i++)
            {
                LogicIdList.Add(i);
            }
        }
        
        //二分查找
        private int GetCurIndexPos(float value)
        {
            if (posHeight == null || posHeight.Count == 0) return 0;
            
            // 如果 value 小于第一个元素，返回 -1
            if (value < posHeight[0])
                return 0;

            // 如果 value 大于等于最后一个元素，返回最后一个索引
            if (value >= posHeight[posHeight.Count - 1])
                return posHeight.Count - 1;

            // 二分查找
            int left = 0;
            int right = posHeight.Count - 1;

            while (left <= right)
            {
                int mid = left + (right - left) / 2;

                if (posHeight[mid] <= value && value < posHeight[mid + 1])
                {
                    return mid; // 找到区间
                }
                else if (posHeight[mid] < value)
                {
                    left = mid + 1; // 在右半部分
                }
                else
                {
                    right = mid - 1; // 在左半部分
                }
            }

            // 理论上不会走到这里，因为前面已经处理了边界情况
            return 0;
        }
        
        private void InitPageView()
        {
            var temp = MailManager.GetMailTypList((ulong)curMailType);

            var idList = new List<ulong>();
            foreach (var node in temp)
            {
                if (!MailManager.IsExistMailDetail(node.Id))
                {
                    idList.Add(node.Id);
                }
            }
            
            MailBriefs.AddRange(temp);
            var isEmpty = MailBriefs.Count <= 0;
            m_txtEmptyTip.gameObject.SetActive(isEmpty);
            m_btnRefresh.gameObject.SetActive(isEmpty);
            m_btnDeleteAll.gameObject.SetActive(!isEmpty);
            m_btnReceiveAll.gameObject.SetActive(!isEmpty);

            if (idList.Count > 0)
            {
                MailManager.C2SMailDetailReq(idList, (resp) =>
                {
                    CheckRechargeContent();
                    ShowMailList();
                });
            }
            else
            {
                CheckRechargeContent();
                ShowMailList();
            }
        }

        private void CheckRechargeContent()
        {
            foreach (var node in MailBriefs)
            {
                if (!MailManager.IsExistPurchaseMail(node.Id))
                {
                    var ok = MailManager.GetMailDetail(node.Id, out var detail);
                    if (ok)
                    {
                        MailManager.ParsePurchaseContent(node.Id, detail.Content);
                    }
                }
            }
        }

        protected override void OnClose(bool isShutdown, object userData)
        {
            base.OnClose(isShutdown, userData);
            m_scrollviewList.onValueChanged.RemoveAllListeners();
            MailManager.SaveMailData();
        }

        public override void OnRefresh(object userData)
        {
            base.OnRefresh(userData);
            var flag = userData.ToString();
            var data = flag.Split("|");
            if (data.Length == 2)
            {
                var key = data[0];
                var value = ulong.Parse(data[1]);
                if (key == "Refresh")
                {
                    InitPageView();
                }
                else if (key == "Add")
                {
                    if (value == (ulong)curMailType)
                    {
                        InitPageView();
                    }
                }
            }
        }

        private void OnBtnBackClick()
        {
            Close();
        }

        private void OnBtnRefreshClick()
        {
            MailManager.C2SMailLoadReq((ulong)curMailType, 0, (resp) => { InitPageView(); });
        }

        private void OnBtnDeleteAllClick()
        {
            MailManager.RequestDeleteAllMail(curMailType, () => { Close(); });
        }

        private void OnBtnReceiveAllClick()
        {
        }

        private void ShowMailList()
        {
            posHeight.Clear();
            float sum = 0;
            foreach (var data in MailBriefs)
            {
                MailManager.CreateMailDetailInfo(data, (result) =>
                {
                    var mailContentPurchaseData = MailManager.GetPurchaseContent(data.Id, result.mailDetail.Content);
                    var hasReward = HasReward(mailContentPurchaseData);
                    if (sum != 0)
                    {
                        sum += itemSpacing;
                    }
                    sum += hasReward ? 1035 : 580;
                    posHeight.Add(sum);
                });
            }
            
            AutoFitContentHeight(MailBriefs);
            var root = contentTrans;
            var sumCount = MailBriefs.Count;

            var triggerCount = initCount <= sumCount - 1 ? initCount : sumCount - 1;
            ToolScriptExtend.RecycleOrCreate(m_goMailContainer, root, sumCount, (i, obj) =>
            {
                UpdateContainerLogic(i, obj);
                if (i == sumCount - 1)
                {
                    LayoutRebuilder.ForceRebuildLayoutImmediate(m_scrollviewList.content);
                }
                
                if (i == triggerCount)
                {
                    m_scrollviewList.onValueChanged.RemoveAllListeners();
                    m_scrollviewList.onValueChanged.AddListener((vec) =>
                    {
                        OnScrollFunc();
                    });
                    OnScrollFunc();
                }
            },0.01f);
        }

        private void UpdateContainerLogic(int index, GameObject obj)
        {
            var data = MailBriefs[index];
            var rect = obj.GetComponent<RectTransform>();
            rect.anchoredPosition = GetContainerPos(index);
            MailManager.CreateMailDetailInfo(data, (result) =>
            {
                var mailContentPurchaseData = MailManager.GetPurchaseContent(data.Id, result.mailDetail.Content);
                var hasReward = HasReward(mailContentPurchaseData);
                rect.SetSizeWithCurrentAnchors(RectTransform.Axis.Vertical, hasReward ? 1035 : 580);
            });
        }
        
        private void ShowLogicItemList()
        {
            var childCount = MailBriefs.Count;
            foreach (var index in lastLogicIdList)
            {
                if (index < childCount && !LogicIdList.Contains(index))
                {
                    var child = contentTrans.GetChild(index);
                    if (child.childCount > 0)
                    {
                        ObjPool.Release(child.GetChild(0).gameObject);
                    }
                }
            }
            
            foreach (var index in LogicIdList)
            {
                if (index < childCount && !lastLogicIdList.Contains(index))
                {
                    var child = contentTrans.GetChild(index); 
                    var obj = ObjPool.Get();
                    obj.transform.SetParent(child);
                    obj.transform.localPosition = Vector3.zero;
                    UpdateItemLogic(index, obj);
                }
            }

            lastLogicIdList.Clear();
            lastLogicIdList.AddRange(LogicIdList);
        }
        
        private void UpdateItemLogic(int index, GameObject obj)
        {
            var data = MailBriefs[index];

            var child = obj.transform;
            //时间记录
            var timer = child.Find("Image/timer").GetComponent<UIText>();
            //大标题
            var txt1 = child.Find("txt1").GetComponent<UIText>();
            //邮件内容
            var txt2 = child.Find("txt2").GetComponent<UIText>();
            //奖励
            var rewardArea = child.Find("reward");
            var rewardScroll = child.Find("reward/Scroll View").GetComponent<ScrollRect>();
            var rewardRoot = rewardScroll.content.transform;

            MailManager.CreateMailDetailInfo(data, (result) =>
            {
                timer.text = MailManager.FormatTimeRecord(data.CreateAt);
                var mailContentPurchaseData = MailManager.GetPurchaseContent(data.Id, result.mailDetail.Content);
                var temp1 = CreateRechargeTitle(data.Title.Args.ToList(), data.Title.Id);
                txt1.text = MailManager.FormatTitleStr(temp1);

                var temp2 = CreateRechargeTitle(data.Title.Args.ToList(), result.mailDetail.ContentId);
                txt2.text = MailManager.FormatTitleStr(temp2);

                var hasReward = HasReward(mailContentPurchaseData);
                rewardArea.gameObject.SetActive(hasReward);

                var rect = obj.GetComponent<RectTransform>();
                rect.SetSizeWithCurrentAnchors(RectTransform.Axis.Vertical, hasReward ? 1035 : 580);

                //展示奖励列表
                var rewards = new List<reward>();
                var list = mailContentPurchaseData.Rewards;

                for (var j = 0; j < list.Count; j++)
                {
                    var info = list[j];
                    var itemID = (itemid)((int)info.Code);
                    var itemCount = info.Amount;
                    rewards.Add(new reward() { item_id = itemID, num = itemCount });
                }

                AutoFitRewardContent(rewards.Count, rewardScroll.content);
                // rewardScroll.enabled = rewards.Count > 8;
                rewardScroll.normalizedPosition = new Vector2(0, 1);
                ToolScriptExtend.RecycleOrCreate(m_goReward, rewardRoot, rewards.Count);
                ToolScriptExtend.ShowRewardList(rewardRoot, rewards, null, 0.7f);
            });
        }

        private MailTitle CreateRechargeTitle(List<MailTitleArgs> argsList, int langId)
        {
            var temp = new MailTitle() { Id = langId };
            payment paymentData;
            foreach (var node in argsList)
            {
                if (ToolScriptExtend.GetConfigById<payment>(int.Parse(node.Value), out paymentData))
                {
                    var titleArgs = new MailTitleArgs
                    {
                        Key = node.Key,
                        Value = ToolScriptExtend.GetLang(paymentData.gift_name)
                    };
                    temp.Args.Add(titleArgs);
                }
            }

            return temp;
        }

        private bool HasReward(MailContentPurchase detail)
        {
            if (detail == null) return false;
            return detail.Rewards != null && detail.Rewards.Count > 0;
        }

        private void AutoFitContentHeight(List<MailBrief> dataList)
        {
            float sum = 0;
            var dataCount = MailBriefs.Count;
            sum += dataCount * mailItemHeight + (dataCount - 1) * itemSpacing;

            foreach (var node in MailBriefs)
            {
                var isOk = MailManager.GetMailDetail(node.Id, out var detail);
                if (isOk)
                {
                    var parseData = MailManager.GetPurchaseContent(node.Id, detail.Content);
                    if (HasReward(parseData))
                    {
                        continue;
                    }
                }

                sum -= 455;
            }

            m_scrollviewList.content.SetSizeWithCurrentAnchors(RectTransform.Axis.Vertical, sum);
        }

        private void AutoFitRewardContent(int count, RectTransform root)
        {
            var layout = root.GetComponent<GridLayoutGroup>();
            var rowCount = GetRowCount(count, layout.constraintCount);
            var sumHeight = layout.padding.top + rowCount * layout.cellSize.y + (rowCount - 1) * layout.spacing.y +
                            layout.padding.bottom;
            root.SetSizeWithCurrentAnchors(RectTransform.Axis.Vertical, sumHeight);
        }

        //计算行数
        private int GetRowCount(int count, int constraintCount)
        {
            var value1 = count / constraintCount;
            var value2 = count % constraintCount;
            var value3 = value2 > 0 ? 1 : 0;
            return value1 + value3;
        }

        private Vector2 GetContainerPos(int index)
        {
            if(index == 0)return Vector2.zero;
            return new Vector2(0,-posHeight[index-1]);
        }
    }
}