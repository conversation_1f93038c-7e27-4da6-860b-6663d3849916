using System;
using System.Collections.Generic;
using DG.Tweening;
using UnityEngine;
using UnityEngine.UI;

namespace Game.Hotfix
{
    public partial class UIScoreProgressForm : UGuiFormEx
    {
        private bool startTimer = false;
        private float timeValue = 10;
        
        protected override void OnInit(object userData)
        {
            base.OnInit(userData);

            InitBind();
            m_goRaceItem.SetActive(false);
        }
        
        protected override void OnOpen(object userData)
        {
            base.OnOpen(userData);
            timeValue = 10;
            Timers.Instance.Add(GetInstanceID().ToString(), 1, (a) =>
            {
                OnTimer();
            },86400);
            if (userData is Action callback)
            {
                callback?.Invoke();
            }
        }

        protected override void OnClose(bool isShutdown, object userData)
        {
            base.OnClose(isShutdown, userData);
            Timers.Instance.Remove(GetInstanceID().ToString());
        }

        public override void OnRefresh(object userData)
        {
            base.OnRefresh(userData);
            
            var data = (ValueTuple<string, object>)userData;
            var flag = data.Item1;
            var param = data.Item2;
            if (flag == "SpecialEvent_ArmsRace")
            {
                if (param is Race.PushRaceScoreChange msg)
                {
                    m_goRaceItem.SetActive(true);
                    ShowRaceProgress(msg);
                }
            }
        }
        
        //计时器逻辑
        private void OnTimer()
        {
            timeValue--;
            if (timeValue <= 0)
            {
                Close();
            }
        }
        
        #region 军备竞赛积分

        private void ShowRaceProgress(Race.PushRaceScoreChange msg)
        {
            var root = m_goRaceItem.transform;
            var slider = root.Find("slider").GetComponent<Slider>();
            var btn = root.Find("btn").GetComponent<UIButton>();

            ShowFlashAnim(root);
            
            slider.DOKill();
            //进度分数
            var scoreList = GameEntry.LogicData.SpecialEventData.GetRaceScoreList();
            var ratioList = new List<float>() { 0.3f, 0.36f, 0.34f };
            var oldRatio = GameEntry.LogicData.SpecialEventData.CalculateProgress(scoreList, ratioList, msg.OldTaskScore);
            var newRatio = GameEntry.LogicData.SpecialEventData.CalculateProgress(scoreList, ratioList, msg.NewTaskScore);
           
            for (var i = 0; i < scoreList.Count; i++)
            {
                var scoreRoot = root.transform.Find($"score{i + 1}");
                var txt = scoreRoot.Find("Text").GetComponent<UIText>();
                var effect = scoreRoot.Find("effect");
                effect.gameObject.SetActive(false);
                txt.text = scoreList[i].ToString();
                SetTxtStatusColor(txt,scoreList[i] <= msg.OldTaskScore);
            }

            var ratioNodeList = CalculateRatioNodeList(ratioList);
            var remainRatioList = new List<int>();
            for (var i = 0; i < ratioNodeList.Count; i++)
            {
                var ratio = ratioNodeList[i];
                if (ratio > oldRatio && ratio <= newRatio)
                {
                    remainRatioList.Add(i);
                }
            }
            
            slider.value = oldRatio;
            Tween instance = null;
            var sliderMove = slider.DOValue(newRatio, 0.8f);
            sliderMove.OnUpdate(() =>
            {
                if (remainRatioList.Count > 0)
                {
                    var tempValue = slider.value;
                    var firstRemainIndex = remainRatioList[0];
                    if (tempValue - ratioNodeList[firstRemainIndex] >= -0.0001f)
                    {
                        var scoreRoot = root.transform.Find($"score{firstRemainIndex + 1}");
                        var txt = scoreRoot.Find("Text").GetComponent<UIText>();
                        var anim = scoreRoot.GetComponent<Animation>();
                        if (anim != null)
                        {
                            anim.Play("UIScoreProgressForm_scores");
                        }
                        SetTxtStatusColor(txt,true);
                        var effect = scoreRoot.Find("effect");
                        effect.gameObject.SetActive(false);
                        effect.gameObject.SetActive(true);
                        remainRatioList.RemoveAt(0);
                    }
                }
            });
            
            sliderMove.OnComplete(() =>
            {
                instance = DOVirtual.DelayedCall(1.5f, () =>
                {
                    m_goRaceItem.SetActive(false);
                });
            });
            
            ToolScriptExtend.BindBtnLogic(btn, () =>
            {
                if (instance != null)
                {
                    instance.Kill();
                }
                slider.DOKill();
                m_goRaceItem.SetActive(false);
            });
            timeValue = 10;
        }
        
        private List<float> CalculateRatioNodeList(List<float> ratioList)
        {
            var ratioNodeList = new List<float>();
            for (var i = 0; i < ratioList.Count; i++)
            {
                float sum = 0;
                for (var j = 0; j <= i; j++)
                {
                    sum += ratioList[j];
                }
                ratioNodeList.Add(sum);
            }
            return ratioNodeList;
        }

        //设置文本状态颜色：分数满足显示绿色，分数未达到显示白色
        private void SetTxtStatusColor(UIText txt,bool isEnough)
        {
            var hex = isEnough ? "#42EE46":"#FFFFFF";
            if (ColorUtility.TryParseHtmlString(hex, out var color))
            {
                txt.color = color;
            }
        }
        
        //展示流光动画
        private void ShowFlashAnim(Transform root)
        {
            var flash = root.Find("flashRoot/flash").GetComponent<RectTransform>();
            flash.DOKill();
            flash.anchoredPosition = Vector2.zero;
            flash.DOAnchorPos(new Vector2(690, 0), 0.45f);
        }
        #endregion
        
    }
}
